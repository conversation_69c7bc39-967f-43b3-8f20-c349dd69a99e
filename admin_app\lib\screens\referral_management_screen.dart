import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/referral_model.dart';
import '../services/referral_service.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';

class ReferralManagementScreen extends StatefulWidget {
  const ReferralManagementScreen({super.key});

  @override
  State<ReferralManagementScreen> createState() => _ReferralManagementScreenState();
}

class _ReferralManagementScreenState extends State<ReferralManagementScreen> {
  bool _isLoading = true;
  List<ReferralModel> _referrals = [];
  ReferralStatus? _selectedStatusFilter;
  Map<String, dynamic> _settings = {};
  Map<String, dynamic> _stats = {};

  final List<ReferralStatus> _statusFilters = [
    ReferralStatus.pending,
    ReferralStatus.completed,
    ReferralStatus.expired,
    ReferralStatus.cancelled,
  ];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final referrals = await ReferralService.getAllReferrals(
        statusFilter: _selectedStatusFilter,
      );
      final settings = await ReferralService.getReferralSettings();
      final stats = await _calculateStats(referrals);

      if (mounted) {
        setState(() {
          _referrals = referrals;
          _settings = settings;
          _stats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  Map<String, dynamic> _calculateStats(List<ReferralModel> referrals) {
    final total = referrals.length;
    final completed = referrals.where((r) => r.status == ReferralStatus.completed).length;
    final pending = referrals.where((r) => r.status == ReferralStatus.pending).length;
    final expired = referrals.where((r) => r.status == ReferralStatus.expired).length;
    final totalRewards = referrals
        .where((r) => r.status == ReferralStatus.completed)
        .fold(0.0, (sum, r) => sum + r.rewardAmount);

    return {
      'total': total,
      'completed': completed,
      'pending': pending,
      'expired': expired,
      'totalRewards': totalRewards,
    };
  }

  void _onStatusFilterChanged(ReferralStatus? status) {
    setState(() {
      _selectedStatusFilter = status;
    });
    _loadData();
  }

  Future<void> _updateReferralStatus(String referralId, ReferralStatus newStatus) async {
    try {
      bool success = false;
      
      switch (newStatus) {
        case ReferralStatus.completed:
          success = await ReferralService.completeReferral(referralId);
          break;
        case ReferralStatus.cancelled:
          // For now, we'll implement a simple update
          success = true; // Placeholder
          break;
        default:
          break;
      }

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Referral status updated successfully')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to update referral status')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => _ReferralSettingsDialog(
        settings: _settings,
        onSave: (newSettings) async {
          final success = await ReferralService.updateReferralSettings(newSettings);
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Settings updated successfully')),
            );
            _loadData();
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Referral Management',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        actions: [
          IconButton(
            onPressed: _showSettingsDialog,
            icon: const Icon(Icons.settings, color: Colors.white),
            tooltip: 'Referral Settings',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : Column(
              children: [
                _buildStatsSection(),
                _buildFiltersSection(),
                Expanded(child: _buildReferralsList()),
              ],
            ),
    );
  }

  Widget _buildStatsSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Total',
              _stats['total']?.toString() ?? '0',
              Icons.people,
              Colors.blue,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: _buildStatCard(
              'Completed',
              _stats['completed']?.toString() ?? '0',
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: _buildStatCard(
              'Pending',
              _stats['pending']?.toString() ?? '0',
              Icons.pending,
              Colors.orange,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: _buildStatCard(
              'Total Rewards',
              '৳${_stats['totalRewards']?.toStringAsFixed(0) ?? '0'}',
              Icons.monetization_on,
              Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              value,
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      child: Row(
        children: [
          const Text(
            'Filter by Status:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: AppConstants.fontSizeMedium,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: DropdownButton<ReferralStatus?>(
              value: _selectedStatusFilter,
              isExpanded: true,
              items: [
                const DropdownMenuItem<ReferralStatus?>(
                  value: null,
                  child: Text('All Statuses'),
                ),
                ..._statusFilters.map((status) {
                  return DropdownMenuItem<ReferralStatus?>(
                    value: status,
                    child: Text(status.value.toUpperCase()),
                  );
                }),
              ],
              onChanged: _onStatusFilterChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReferralsList() {
    if (_referrals.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.people_outline,
        title: 'No Referrals Found',
        subtitle: 'No referrals match the current filter criteria.',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        itemCount: _referrals.length,
        itemBuilder: (context, index) {
          final referral = _referrals[index];
          return _buildReferralCard(referral);
        },
      ),
    );
  }

  Widget _buildReferralCard(ReferralModel referral) {
    Color statusColor;
    IconData statusIcon;
    
    switch (referral.status) {
      case ReferralStatus.completed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case ReferralStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        break;
      case ReferralStatus.expired:
        statusColor = Colors.red;
        statusIcon = Icons.expired;
        break;
      case ReferralStatus.cancelled:
        statusColor = Colors.grey;
        statusIcon = Icons.cancel;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Code: ${referral.referralCode}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: AppConstants.fontSizeMedium,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        'Referrer: ${referral.referrerName}',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        referral.referrerEmail,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingSmall,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, color: statusColor, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        referral.status.value.toUpperCase(),
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: AppConstants.fontSizeSmall,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Referee: ${referral.refereeName}',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        referral.refereeEmail,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                if (referral.status == ReferralStatus.completed)
                  Text(
                    '৳${referral.rewardAmount.toStringAsFixed(0)}',
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                      fontSize: AppConstants.fontSizeLarge,
                    ),
                  ),
              ],
            ),
            if (referral.status == ReferralStatus.pending) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _updateReferralStatus(referral.id, ReferralStatus.completed),
                      icon: const Icon(Icons.check, size: 16),
                      label: const Text('Complete'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _updateReferralStatus(referral.id, ReferralStatus.cancelled),
                      icon: const Icon(Icons.cancel, size: 16),
                      label: const Text('Cancel'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _ReferralSettingsDialog extends StatefulWidget {
  final Map<String, dynamic> settings;
  final Function(Map<String, dynamic>) onSave;

  const _ReferralSettingsDialog({
    required this.settings,
    required this.onSave,
  });

  @override
  State<_ReferralSettingsDialog> createState() => _ReferralSettingsDialogState();
}

class _ReferralSettingsDialogState extends State<_ReferralSettingsDialog> {
  late TextEditingController _rewardAmountController;
  late TextEditingController _expiryDaysController;
  late TextEditingController _minimumPurchaseController;
  late bool _isEnabled;

  @override
  void initState() {
    super.initState();
    _rewardAmountController = TextEditingController(
      text: widget.settings['rewardAmount']?.toString() ?? '50',
    );
    _expiryDaysController = TextEditingController(
      text: widget.settings['expiryDays']?.toString() ?? '30',
    );
    _minimumPurchaseController = TextEditingController(
      text: widget.settings['minimumPurchaseAmount']?.toString() ?? '100',
    );
    _isEnabled = widget.settings['isEnabled'] ?? true;
  }

  @override
  void dispose() {
    _rewardAmountController.dispose();
    _expiryDaysController.dispose();
    _minimumPurchaseController.dispose();
    super.dispose();
  }

  void _save() {
    final settings = {
      'rewardAmount': double.tryParse(_rewardAmountController.text) ?? 50.0,
      'expiryDays': int.tryParse(_expiryDaysController.text) ?? 30,
      'minimumPurchaseAmount': double.tryParse(_minimumPurchaseController.text) ?? 100.0,
      'isEnabled': _isEnabled,
    };

    widget.onSave(settings);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Referral Settings'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('Enable Referral Program'),
              value: _isEnabled,
              onChanged: (value) {
                setState(() {
                  _isEnabled = value;
                });
              },
            ),
            TextField(
              controller: _rewardAmountController,
              decoration: const InputDecoration(
                labelText: 'Reward Amount (৳)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: _expiryDaysController,
              decoration: const InputDecoration(
                labelText: 'Expiry Days',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: _minimumPurchaseController,
              decoration: const InputDecoration(
                labelText: 'Minimum Purchase Amount (৳)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _save,
          child: const Text('Save'),
        ),
      ],
    );
  }
}
